"use client";
import React from "react";
import { Bot } from "lucide-react";

type CandidateWithAgentProps = {
  className?: string;
  candidateName?: string;
  jobTitle?: string;
  useAgent?: boolean;
  message?: string;
  onVideoReady?: () => void;
  onVideoEnd?: () => void;
  useStreaming?: boolean;
  avatarMode?: "standard" | "streaming" | "live";
};

const CandidateWithAgent: React.FC<CandidateWithAgentProps> = ({
  className = "",
  candidateName = "Jonathan",
  jobTitle = "Insurance Agent",
  // useAgent = true,

}) => {
  // Remove D-ID agent logic since we're using the interview API now

  return (
    <div className={`relative ${className}`}>
      <div className="w-full h-full bg-gradient-to-br from-blue-50 to-indigo-100 rounded-2xl flex flex-col items-center justify-center overflow-hidden">
        <div className="text-center">
          <div className="w-32 h-32 bg-gray-300 rounded-full flex items-center justify-center mx-auto mb-4">
            <Bot className="w-16 h-16 text-gray-600" />
          </div>
          <h3 className="font-semibold text-lg text-gray-800">
            {candidateName}
          </h3>
          <p className="text-sm text-gray-600 mt-1">
            {jobTitle} Interview
          </p>
        </div>
      </div>
    </div>
  );
};

export default CandidateWithAgent;
