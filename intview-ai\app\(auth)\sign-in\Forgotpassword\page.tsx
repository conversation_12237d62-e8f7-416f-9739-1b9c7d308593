"use client";

import { ForgotPasswordSchema } from "@/lib/validations";
import PasswordForm from "@/components/forms/forgetpasswordprocess";
import Image from "next/image";
const ForgotPasswordPage = () => {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const handleSubmit = async (data: any) => {
    console.log("Submitted Data:", data);
    return { success: true };
  };

  return (
    <div className="min-h-screen lg:h-[150vh] lg:relative">
      {/* Background image container with 140% height */}
      <div className="hidden lg:block lg:absolute lg:top-0 lg:left-0 lg:w-[60%] lg:h-[150vh] lg:min-h-[150vh]">
        <Image
          src="/images/bgauthimg.png"
          alt="auth"
          fill
          className="object-cover"
          priority
        />
      </div>
      {/* Mobile layout */}
      <div className="lg:hidden">
        {/* Top image for mobile/tablet */}
        <div className="relative w-full h-60 overflow-hidden rounded-b-4xl">
          <Image
            src="/images/bgauthimg.png"
            alt="auth"
            fill
            className="object-cover"
            priority
          />
        </div>

        {/* Form Section for mobile */}
          <div className="bg-white px-4 sm:px-6 py-8">
          <div className="mx-auto max-w-sm sm:max-w-lg md:max-w-3xl">
            <PasswordForm
              mode="forgot"
              schema={ForgotPasswordSchema}
              defaultValues={{ email: "" }}
              onSubmit={handleSubmit}
            />
          </div>
        </div>
      </div>
      {/* Desktop Form Section with proper rounded corners */}
      <div className="hidden lg:block lg:absolute lg:right-0 lg:top-0 lg:w-[48%] lg:h-[150vh] lg:min-h-[150vh]">
        <div className="bg-white lg:rounded-l-4xl h-full">
          <div className="flex justify-center px-4 sm:px-6 py-13 h-full">
            <div className="w-full max-w-md">
              <PasswordForm
                mode="forgot"
                schema={ForgotPasswordSchema}
                defaultValues={{ email: "" }}
                onSubmit={handleSubmit}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ForgotPasswordPage;
