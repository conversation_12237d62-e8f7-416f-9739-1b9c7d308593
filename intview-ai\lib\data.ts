// lib/data/jobs.ts

export const jobData = [
  {
    title: "UX/UI Designer for AI–Interview Web App",
    price: "$100 – $300",
    location: "New York",
    jobType: "Onsite / Remote",
    description:
      "We’re building an AI-powered Interview Web App designed to help users prepare for job interviews with smart.",
    status: "Active",
  },
  {
    title: "Frontend Developer - React + TypeScript",
    price: "$500 – $1000",
    location: "Remote",
    jobType: "Remote",
    description:
      "Join our team to build cutting-edge SaaS tools for startups. Must be experienced with React and modern front-end tools.",
    status: "Active",
  },
  {
    title: "Frontend Developer - React + TypeScript",
    price: "$500 – $1000",
    location: "Remote",
    jobType: "Remote",
    description:
      "Join our team to build cutting-edge SaaS tools for startups. Must be experienced with React and modern front-end tools.",
    status: "Active",
  },
  {
    title: "Frontend Developer - React + TypeScript",
    price: "$500 – $1000",
    location: "Remote",
    jobType: "Remote",
    description:
      "Join our team to build cutting-edge SaaS tools for startups. Must be experienced with React and modern front-end tools.",
    status: "Active",
  },
  {
    title: "Frontend Developer - React + TypeScript",
    price: "$500 – $1000",
    location: "Remote",
    jobType: "Remote",
    description:
      "Join our team to build cutting-edge SaaS tools for startups. Must be experienced with React and modern front-end tools.",
    status: "Active",
  },
  {
    title: "Frontend Developer - React + TypeScript",
    price: "$500 – $1000",
    location: "Remote",
    jobType: "Remote",
    description:
      "Join our team to build cutting-edge SaaS tools for startups. Must be experienced with React and modern front-end tools.",
    status: "Active",
  },
  {
    title: "Frontend Developer - React + TypeScript",
    price: "$500 – $1000",
    location: "Remote",
    jobType: "Remote",
    description:
      "Join our team to build cutting-edge SaaS tools for startups. Must be experienced with React and modern front-end tools.",
    status: "Active",
  },
  
  // Add more...
];
