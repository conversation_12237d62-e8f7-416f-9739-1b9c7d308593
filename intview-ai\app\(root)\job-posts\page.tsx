// app/job-posts/page.tsx

import { Input } from "@/components/ui/input";
import { Search, Briefcase } from "lucide-react";
import { jobData } from "@/lib/data";

import {
  <PERSON>,
  CardContent,
  CardDescription,
  <PERSON><PERSON><PERSON>er,
  Card<PERSON><PERSON>er,
  Card<PERSON>itle,
} from "@/components/ui/card";

const JobPosts = () => {
  return (
    <div className="flex flex-col gap-7">
      <h1 className="font-poppins font-semibold text-[22px] leading-[100%] tracking-[1%] text-gray-900">
        Recent Job Posts
      </h1>

      {/* Search Input */}
      <div className="relative w-full h-[53px] max-w-[490px] xl:max-w-[792px] md:max-w-full">
        <Input
          type="search"
          placeholder="Search..."
          className="
            pl-3 pr-10 h-full w-full
            bg-[#FAFAFA] text-gray-700 placeholder-[#B3B3B3]
            rounded-[10px] border-[1px] border-[#E0E0E0]
            text-base px-4 py-[4px]
            appearance-none
          "
        />
        <Search className="absolute right-4 top-1/2 -translate-y-1/2 text-[#B3B3B3] h-5 w-5" />
      </div>

      {/* Job Cards Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-2 2xl:grid-cols-3 gap-6 w-full">
        {jobData.map((job, index) => (
          <Card
            key={index}
            className="w-full shadow-md rounded-xl border border-gray-200 p-4" // reduce outer padding
          >
            <CardHeader className="pb-1 px-0">
              <div className="flex items-start justify-between">
                <CardTitle className="text-base font-semibold text-gray-900">
                  {job.title}
                </CardTitle>
                <span className="rounded-full bg-[#CCFFB1] px-3 py-0.5 text-xs font-medium text-[#144100]">
                  {job.status}
                </span>
              </div>
              <CardDescription className="mt-1 text-sm text-[#000000] flex flex-wrap items-center gap-2">
                <span>{job.price}</span>
                <span>&bull;</span>
                <span className="flex items-center gap-1">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-4 w-4"
                    viewBox="0 0 24 24"
                    fill="black"
                  >
                    <path d="M12 2C8.686 2 6 4.686 6 8c0 5.25 6 12 6 12s6-6.75 6-12c0-3.314-2.686-6-6-6z" />
                    <circle cx="12" cy="8" r="2.5" fill="white" />
                  </svg>
                  {job.location}
                </span>
                <span>&bull;</span>
                <span className="flex items-center gap-1">
                  <Briefcase className="h-4 w-4" />
                  {job.jobType}
                </span>
              </CardDescription>
            </CardHeader>

            <CardContent className="pt-0 px-0 pb-2">
              <p className="text-sm text-gray-600 line-clamp-2">
                {job.description}
              </p>
            </CardContent>

            <CardFooter className="px-0 pt-0">
              <button className="w-full rounded-full bg-[#6938EF] px-4 py-2.5 text-sm font-semibold text-white hover:opacity-80 transition hover:cursor-pointer">
                Apply Now
              </button>
            </CardFooter>
          </Card>
        ))}
      </div>
    </div>
  );
};

export default JobPosts;
