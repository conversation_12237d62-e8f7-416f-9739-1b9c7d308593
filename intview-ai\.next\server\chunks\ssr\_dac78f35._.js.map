{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\r\n        destructive:\r\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\r\n        outline:\r\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\r\n        secondary:\r\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\r\n        ghost:\r\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\r\n        link: \"text-primary underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\r\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\r\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\r\n        icon: \"size-9\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nfunction Button({\r\n  className,\r\n  variant,\r\n  size,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"button\"> &\r\n  VariantProps<typeof buttonVariants> & {\r\n    asChild?: boolean\r\n  }) {\r\n  const Comp = asChild ? Slot : \"button\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"button\"\r\n      className={cn(buttonVariants({ variant, size, className }))}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Button, buttonVariants }\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 80, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Label({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\r\n  return (\r\n    <LabelPrimitive.Root\r\n      data-slot=\"label\"\r\n      className={cn(\r\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Label }\r\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,8OAAC,iKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 108, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/ui/form.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport * as LabelPrimitive from \"@radix-ui/react-label\";\r\nimport { Slot } from \"@radix-ui/react-slot\";\r\nimport {\r\n  Controller,\r\n  FormProvider,\r\n  useFormContext,\r\n  useFormState,\r\n  type ControllerProps,\r\n  type FieldPath,\r\n  type FieldValues,\r\n} from \"react-hook-form\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\nimport { Label } from \"@/components/ui/label\";\r\n\r\nconst Form = FormProvider;\r\n\r\ntype FormFieldContextValue<\r\n  TFieldValues extends FieldValues = FieldValues,\r\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\r\n> = {\r\n  name: TName;\r\n};\r\n\r\nconst FormFieldContext = React.createContext<FormFieldContextValue>(\r\n  {} as FormFieldContextValue\r\n);\r\n\r\nconst FormField = <\r\n  TFieldValues extends FieldValues = FieldValues,\r\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\r\n>({\r\n  ...props\r\n}: ControllerProps<TFieldValues, TName>) => {\r\n  return (\r\n    <FormFieldContext.Provider value={{ name: props.name }}>\r\n      <Controller {...props} />\r\n    </FormFieldContext.Provider>\r\n  );\r\n};\r\n\r\nconst useFormField = () => {\r\n  const fieldContext = React.useContext(FormFieldContext);\r\n  const itemContext = React.useContext(FormItemContext);\r\n  const { getFieldState } = useFormContext();\r\n  const formState = useFormState({ name: fieldContext.name });\r\n  const fieldState = getFieldState(fieldContext.name, formState);\r\n\r\n  if (!fieldContext) {\r\n    throw new Error(\"useFormField should be used within <FormField>\");\r\n  }\r\n\r\n  const { id } = itemContext;\r\n\r\n  return {\r\n    id,\r\n    name: fieldContext.name,\r\n    formItemId: `${id}-form-item`,\r\n    formDescriptionId: `${id}-form-item-description`,\r\n    formMessageId: `${id}-form-item-message`,\r\n    ...fieldState,\r\n  };\r\n};\r\n\r\ntype FormItemContextValue = {\r\n  id: string;\r\n};\r\n\r\nconst FormItemContext = React.createContext<FormItemContextValue>(\r\n  {} as FormItemContextValue\r\n);\r\n\r\nfunction FormItem({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  const id = React.useId();\r\n\r\n  return (\r\n    <FormItemContext.Provider value={{ id }}>\r\n      <div\r\n        data-slot=\"form-item\"\r\n        className={cn(\"grid gap-3 \", className)}\r\n        {...props}\r\n      />\r\n    </FormItemContext.Provider>\r\n  );\r\n}\r\n\r\nfunction FormLabel({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\r\n  const { error, formItemId } = useFormField();\r\n\r\n  return (\r\n    <Label\r\n      data-slot=\"form-label\"\r\n      data-error={!!error}\r\n      className={cn(\"data-[error=true]:text-destructive\", className)}\r\n      htmlFor={formItemId}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction FormControl({ ...props }: React.ComponentProps<typeof Slot>) {\r\n  const { error, formItemId, formDescriptionId, formMessageId } =\r\n    useFormField();\r\n\r\n  return (\r\n    <Slot\r\n      data-slot=\"form-control\"\r\n      id={formItemId}\r\n      aria-describedby={\r\n        !error\r\n          ? `${formDescriptionId}`\r\n          : `${formDescriptionId} ${formMessageId}`\r\n      }\r\n      aria-invalid={!!error}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction FormDescription({ className, ...props }: React.ComponentProps<\"p\">) {\r\n  const { formDescriptionId } = useFormField();\r\n\r\n  return (\r\n    <p\r\n      data-slot=\"form-description\"\r\n      id={formDescriptionId}\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction FormMessage({ className, ...props }: React.ComponentProps<\"p\">) {\r\n  const { error, formMessageId } = useFormField();\r\n  const body = error ? String(error?.message ?? \"\") : props.children;\r\n\r\n  if (!body) return null;\r\n\r\n  return (\r\n    <p\r\n      data-slot=\"form-message\"\r\n      id={formMessageId}\r\n      className={cn(\"text-sm !text-red-600\", className)} // Force red color\r\n      {...props}\r\n    >\r\n      {body}\r\n    </p>\r\n  );\r\n}\r\n\r\nexport {\r\n  useFormField,\r\n  Form,\r\n  FormItem,\r\n  FormLabel,\r\n  FormControl,\r\n  FormDescription,\r\n  FormMessage,\r\n  FormField,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAEA;AAEA;AACA;AAUA;AACA;AAhBA;;;;;;;AAkBA,MAAM,OAAO,8JAAA,CAAA,eAAY;AASzB,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EACzC,CAAC;AAGH,MAAM,YAAY,CAGhB,EACA,GAAG,OACkC;IACrC,qBACE,8OAAC,iBAAiB,QAAQ;QAAC,OAAO;YAAE,MAAM,MAAM,IAAI;QAAC;kBACnD,cAAA,8OAAC,8JAAA,CAAA,aAAU;YAAE,GAAG,KAAK;;;;;;;;;;;AAG3B;AAEA,MAAM,eAAe;IACnB,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;IACtC,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;IACrC,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,8JAAA,CAAA,iBAAc,AAAD;IACvC,MAAM,YAAY,CAAA,GAAA,8JAAA,CAAA,eAAY,AAAD,EAAE;QAAE,MAAM,aAAa,IAAI;IAAC;IACzD,MAAM,aAAa,cAAc,aAAa,IAAI,EAAE;IAEpD,IAAI,CAAC,cAAc;QACjB,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,EAAE,EAAE,EAAE,GAAG;IAEf,OAAO;QACL;QACA,MAAM,aAAa,IAAI;QACvB,YAAY,GAAG,GAAG,UAAU,CAAC;QAC7B,mBAAmB,GAAG,GAAG,sBAAsB,CAAC;QAChD,eAAe,GAAG,GAAG,kBAAkB,CAAC;QACxC,GAAG,UAAU;IACf;AACF;AAMA,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EACxC,CAAC;AAGH,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAoC;IACpE,MAAM,KAAK,CAAA,GAAA,qMAAA,CAAA,QAAW,AAAD;IAErB,qBACE,8OAAC,gBAAgB,QAAQ;QAAC,OAAO;YAAE;QAAG;kBACpC,cAAA,8OAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAC5B,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,UAAU,EACjB,SAAS,EACT,GAAG,OAC8C;IACjD,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG;IAE9B,qBACE,8OAAC,0HAAA,CAAA,QAAK;QACJ,aAAU;QACV,cAAY,CAAC,CAAC;QACd,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACpD,SAAS;QACR,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,GAAG,OAA0C;IAClE,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,iBAAiB,EAAE,aAAa,EAAE,GAC3D;IAEF,qBACE,8OAAC,gKAAA,CAAA,OAAI;QACH,aAAU;QACV,IAAI;QACJ,oBACE,CAAC,QACG,GAAG,mBAAmB,GACtB,GAAG,kBAAkB,CAAC,EAAE,eAAe;QAE7C,gBAAc,CAAC,CAAC;QACf,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAkC;IACzE,MAAM,EAAE,iBAAiB,EAAE,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,IAAI;QACJ,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAkC;IACrE,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,GAAG;IACjC,MAAM,OAAO,QAAQ,OAAO,OAAO,WAAW,MAAM,MAAM,QAAQ;IAElE,IAAI,CAAC,MAAM,OAAO;IAElB,qBACE,8OAAC;QACC,aAAU;QACV,IAAI;QACJ,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACtC,GAAG,KAAK;kBAER;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 258, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\r\n  return (\r\n    <input\r\n      type={type}\r\n      data-slot=\"input\"\r\n      className={cn(\r\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\r\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Input }\r\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 284, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/constants/routes.ts"], "sourcesContent": ["const ROUTES = {\r\n  HOME: \"/\",\r\n  SIGN_IN: \"/sign-in\",\r\n  SIGN_UP: \"/sign-up\",\r\n};\r\n\r\nexport default ROUTES;\r\n"], "names": [], "mappings": ";;;AAAA,MAAM,SAAS;IACb,MAAM;IACN,SAAS;IACT,SAAS;AACX;uCAEe", "debugId": null}}, {"offset": {"line": 299, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/forms/AuthForm.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState } from \"react\";\r\nimport { zodResolver } from \"@hookform/resolvers/zod\";\r\nimport {\r\n  DefaultValues,\r\n  FieldValues,\r\n  Path,\r\n  SubmitHandler,\r\n  useForm,\r\n} from \"react-hook-form\";\r\nimport { z, ZodType } from \"zod\";\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\r\nimport {\r\n  Form,\r\n  FormControl,\r\n  FormField,\r\n  FormItem,\r\n  FormLabel,\r\n  FormMessage,\r\n} from \"@/components/ui/form\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport Link from \"next/link\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { toast } from \"sonner\";\r\nimport ROUTES from \"@/constants/routes\";\r\nimport Image from \"next/image\";\r\n\r\nexport interface AuthFormProps<T extends FieldValues> {\r\n  schema: ZodType<T, T>;\r\n  defaultValues: T;\r\n  onSubmit: (data: T) => Promise<{ success: boolean }>;\r\n  formType: \"SIGN_IN\" | \"SIGN_UP\" | \"RESET_PASSWORD\";\r\n  heading: string;\r\n  placeholderValues: { [key: string]: string };\r\n}\r\n\r\nconst AuthForm = <T extends FieldValues>({\r\n  schema,\r\n  defaultValues,\r\n  formType,\r\n  heading,\r\n  placeholderValues,\r\n}: AuthFormProps<T>) => {\r\n  const router = useRouter();\r\n  const form = useForm<z.infer<typeof schema>>({\r\n    resolver: zodResolver(schema),\r\n    defaultValues: defaultValues as DefaultValues<T>,\r\n    mode: \"onChange\", // ensures isValid updates live\r\n  });\r\n\r\n  const [focusedField, setFocusedField] = useState<string | null>(null);\r\n  const [enableSubmitButton, setEnableSubmitButton] = useState(false);\r\n  const [showPassword, setShowPassword] = useState(false); // State to toggle password visibility\r\n  const [isLoading, setIsLoading] = useState(false);\r\n\r\n  const handleSubmit: SubmitHandler<T> = async (data) => {\r\n    setIsLoading(true);\r\n\r\n    try {\r\n      // Simulate API call delay\r\n      await new Promise(resolve => setTimeout(resolve, 1500));\r\n\r\n      // Add your authentication logic here\r\n      // For now, simulate successful authentication\r\n\r\n      // Show success toast\r\n      const successMessage = formType === \"SIGN_IN\"\r\n        ? \"Successfully signed in! Welcome back.\"\r\n        : \"Account created successfully! Welcome to AI Interview.\";\r\n\r\n      toast.success(successMessage);\r\n\r\n      // Navigate to home page after a brief delay to show the toast\r\n      setTimeout(() => {\r\n        router.push(ROUTES.HOME);\r\n      }, 1000);\r\n\r\n    } catch (error) {\r\n      console.error(\"Authentication error:\", error);\r\n\r\n      // Show error toast\r\n      const errorMessage = formType === \"SIGN_IN\"\r\n        ? \"Sign in failed. Please check your credentials.\"\r\n        : \"Account creation failed. Please try again.\";\r\n\r\n      toast.error(errorMessage);\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  let buttonText = \"Continue\";\r\n  if (formType === \"SIGN_IN\") buttonText = \"Sign In\";\r\n  else if (formType === \"SIGN_UP\") buttonText = \"Create an Account\";\r\n  else if (formType === \"RESET_PASSWORD\") buttonText = \"Reset Password\";\r\n\r\n  return (\r\n    <Form {...form}>\r\n      <form\r\n        onSubmit={form.handleSubmit(handleSubmit)}\r\n        className=\"space-y-4 pt-5 px-0 w-full\"\r\n      >\r\n        <h1 className=\"text-[34px] leading-[41px] font-semibold font-poppins text-gray-900 mb-8\">\r\n          {heading}\r\n        </h1>\r\n\r\n        {Object.keys(defaultValues).map((fieldName) => (\r\n          <FormField\r\n            key={fieldName}\r\n            control={form.control}\r\n            name={fieldName as Path<T>}\r\n            render={({ field }) => {\r\n              const error = form.formState.errors[field.name];\r\n              const isTouched = form.getFieldState(field.name).isTouched;\r\n              const isFocused = focusedField === field.name;\r\n              const isValid = isTouched && !error;\r\n\r\n              const borderClass = isFocused ? \" \" : isValid ? \"\" : \"\";\r\n\r\n              return (\r\n                <FormItem className=\"flex w-full flex-col gap-3.5\">\r\n                  <FormLabel className=\"paragraph-medium text-dark400_light700\">\r\n                    {field.name === \"email\"\r\n                      ? \"Email Address\"\r\n                      : field.name.charAt(0).toUpperCase() +\r\n                        field.name.slice(1)}\r\n                  </FormLabel>\r\n                  <FormControl>\r\n                    <div className=\"relative w-full\">\r\n                      <Input\r\n                        type={\r\n                          field.name.toLowerCase().includes(\"password\") &&\r\n                          !showPassword\r\n                            ? \"password\"\r\n                            : \"text\"\r\n                        }\r\n                        {...field}\r\n                        onFocus={() => setFocusedField(field.name)}\r\n                        onBlur={() => setFocusedField(null)}\r\n                        className={`paragraph-regular background-light900_dark300 text-dark300_light700 min-h-12 rounded-1.5 border focus:outline-none w-full ${borderClass}`}\r\n                        placeholder={placeholderValues[field.name] || \"\"}\r\n                      />\r\n                      {field.name.toLowerCase().includes(\"password\") && (\r\n                        <button\r\n                          type=\"button\"\r\n                          onClick={() => setShowPassword(!showPassword)}\r\n                          className=\"absolute right-4 top-1/2 transform -translate-y-1/2 hover:cursor-pointer\"\r\n                        >\r\n                          <Image\r\n                            src=\"/images/eye.png\" // Replace this with your eye icon path\r\n                            alt=\"Toggle Password Visibility\"\r\n                            width={20} // Adjust size as needed\r\n                            height={20} // Adjust size as needed\r\n                          />\r\n                        </button>\r\n                      )}\r\n                    </div>\r\n                  </FormControl>\r\n                  <FormMessage />\r\n                </FormItem>\r\n              );\r\n            }}\r\n          />\r\n        ))}\r\n\r\n        {formType === \"SIGN_IN\" && (\r\n          <div className=\"flex justify-end mt-5\">\r\n            <Link\r\n              href={\"/sign-in/Forgotpassword\"}\r\n              className=\"font-medium underline text-[#7B61FF] mb-14\"\r\n            >\r\n              Forgot Password?\r\n            </Link>\r\n          </div>\r\n        )}\r\n\r\n        {formType === \"SIGN_UP\" && (\r\n          <div className=\"flex items-start gap-3 text-sm\">\r\n            <input\r\n              type=\"checkbox\"\r\n              id=\"terms\"\r\n              className=\"mt-1\"\r\n              onChange={(e) => setEnableSubmitButton(e.target.checked)}\r\n            />\r\n            <label htmlFor=\"terms\" className=\"text-gray-700\">\r\n              I agree to all the{\" \"}\r\n              <span className=\"text-[#6938EF] cursor-pointer\">Terms</span> of\r\n              service and{\" \"}\r\n              <span className=\"text-[#6938EF] cursor-pointer\">Privacy</span>{\" \"}\r\n              <span className=\"text-[#6938EF] cursor-pointer\">Policies</span>\r\n            </label>\r\n          </div>\r\n        )}\r\n        <div className=\"flex justify-center w-full\">\r\n          <Button\r\n            className=\"primary-button paragraph-medium w-full max-w-[370px] min-h-12 rounded-4xl px-4 py-3 font-inter !text-light-900 text-white hover:cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed\"\r\n            disabled={\r\n              isLoading ||\r\n              form.formState.isSubmitting ||\r\n              !form.formState.isValid ||\r\n              (formType === \"SIGN_UP\" && !enableSubmitButton)\r\n            }\r\n          >\r\n            {isLoading || form.formState.isSubmitting\r\n              ? formType === \"SIGN_IN\"\r\n                ? \"Signing In...\"\r\n                : formType === \"SIGN_UP\"\r\n                ? \"Creating Account...\"\r\n                : \"Processing...\"\r\n              : buttonText}\r\n          </Button>\r\n        </div>\r\n\r\n        {formType === \"SIGN_IN\" ? (\r\n          <p className=\"text-center mb-14\">\r\n            {\"Don't have an account? \"}\r\n            <Link\r\n              href={ROUTES.SIGN_UP}\r\n              className=\" font-semibold underline text-[#7B61FF]\"\r\n            >\r\n              Sign Up\r\n            </Link>\r\n          </p>\r\n        ) : (\r\n          <p className=\"text-center\">\r\n            Already have an account?{\" \"}\r\n            <Link\r\n              href={ROUTES.SIGN_IN}\r\n              className=\"underline text-[#6938EF] font-medium \"\r\n            >\r\n              Login\r\n            </Link>\r\n          </p>\r\n        )}\r\n\r\n        <div\r\n          className={`flex items-center justify-around flex-col gap-2 sm:flex-row sm:gap-0${\r\n            formType === \"SIGN_IN\" ? \"mt-16\" : \"mt-16\"\r\n          }`}\r\n        >\r\n          <span className=\"text-md text-[#000000]\">Or continue with</span>\r\n          <div className=\"flex items-center gap-2\">\r\n            <button\r\n              type=\"button\"\r\n              className=\"border rounded-full hover:bg-gray-700 hover:cursor-pointer transition\"\r\n              aria-label=\"Continue with Google\"\r\n            >\r\n              <Image\r\n                src=\"/icons/google.svg\"\r\n                alt=\"Google\"\r\n                width={32}\r\n                height={32}\r\n              />\r\n            </button>\r\n            <button\r\n              type=\"button\"\r\n              className=\"border rounded-full hover:bg-gray-700 hover:cursor-pointer transition\"\r\n              aria-label=\"Continue with Facebook\"\r\n            >\r\n              <Image\r\n                src=\"/icons/facebook.svg\"\r\n                alt=\"Facebook\"\r\n                width={32}\r\n                height={32}\r\n              />\r\n            </button>\r\n            <button\r\n              type=\"button\"\r\n              className=\"border rounded-full  transition hover:bg-gray-700 hover:cursor-pointer\"\r\n              aria-label=\"Continue with Apple\"\r\n            >\r\n              <Image\r\n                src=\"/icons/apple.svg\"\r\n                alt=\"Apple\"\r\n                width={32}\r\n                height={32}\r\n              />\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </form>\r\n    </Form>\r\n  );\r\n};\r\n\r\nexport default AuthForm;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAQA;AACA;AAQA;AACA;AACA;AACA;AACA;AACA;AA1BA;;;;;;;;;;;;;AAqCA,MAAM,WAAW,CAAwB,EACvC,MAAM,EACN,aAAa,EACb,QAAQ,EACR,OAAO,EACP,iBAAiB,EACA;IACjB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAA0B;QAC3C,UAAU,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe;QACf,MAAM;IACR;IAEA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAChE,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ,sCAAsC;IAC/F,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,eAAiC,OAAO;QAC5C,aAAa;QAEb,IAAI;YACF,0BAA0B;YAC1B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,qCAAqC;YACrC,8CAA8C;YAE9C,qBAAqB;YACrB,MAAM,iBAAiB,aAAa,YAChC,0CACA;YAEJ,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAEd,8DAA8D;YAC9D,WAAW;gBACT,OAAO,IAAI,CAAC,mHAAA,CAAA,UAAM,CAAC,IAAI;YACzB,GAAG;QAEL,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YAEvC,mBAAmB;YACnB,MAAM,eAAe,aAAa,YAC9B,mDACA;YAEJ,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,aAAa;QACf;IACF;IAEA,IAAI,aAAa;IACjB,IAAI,aAAa,WAAW,aAAa;SACpC,IAAI,aAAa,WAAW,aAAa;SACzC,IAAI,aAAa,kBAAkB,aAAa;IAErD,qBACE,8OAAC,yHAAA,CAAA,OAAI;QAAE,GAAG,IAAI;kBACZ,cAAA,8OAAC;YACC,UAAU,KAAK,YAAY,CAAC;YAC5B,WAAU;;8BAEV,8OAAC;oBAAG,WAAU;8BACX;;;;;;gBAGF,OAAO,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC,0BAC/B,8OAAC,yHAAA,CAAA,YAAS;wBAER,SAAS,KAAK,OAAO;wBACrB,MAAM;wBACN,QAAQ,CAAC,EAAE,KAAK,EAAE;4BAChB,MAAM,QAAQ,KAAK,SAAS,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC;4BAC/C,MAAM,YAAY,KAAK,aAAa,CAAC,MAAM,IAAI,EAAE,SAAS;4BAC1D,MAAM,YAAY,iBAAiB,MAAM,IAAI;4BAC7C,MAAM,UAAU,aAAa,CAAC;4BAE9B,MAAM,cAAc,YAAY,MAAM,UAAU,KAAK;4BAErD,qBACE,8OAAC,yHAAA,CAAA,WAAQ;gCAAC,WAAU;;kDAClB,8OAAC,yHAAA,CAAA,YAAS;wCAAC,WAAU;kDAClB,MAAM,IAAI,KAAK,UACZ,kBACA,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,WAAW,KAChC,MAAM,IAAI,CAAC,KAAK,CAAC;;;;;;kDAEvB,8OAAC,yHAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,0HAAA,CAAA,QAAK;oDACJ,MACE,MAAM,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,eAClC,CAAC,eACG,aACA;oDAEL,GAAG,KAAK;oDACT,SAAS,IAAM,gBAAgB,MAAM,IAAI;oDACzC,QAAQ,IAAM,gBAAgB;oDAC9B,WAAW,CAAC,0HAA0H,EAAE,aAAa;oDACrJ,aAAa,iBAAiB,CAAC,MAAM,IAAI,CAAC,IAAI;;;;;;gDAE/C,MAAM,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,6BACjC,8OAAC;oDACC,MAAK;oDACL,SAAS,IAAM,gBAAgB,CAAC;oDAChC,WAAU;8DAEV,cAAA,8OAAC,6HAAA,CAAA,UAAK;wDACJ,KAAI,kBAAkB,uCAAuC;;wDAC7D,KAAI;wDACJ,OAAO;wDACP,QAAQ;;;;;;;;;;;;;;;;;;;;;;kDAMlB,8OAAC,yHAAA,CAAA,cAAW;;;;;;;;;;;wBAGlB;uBArDK;;;;;gBAyDR,aAAa,2BACZ,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;wBACH,MAAM;wBACN,WAAU;kCACX;;;;;;;;;;;gBAMJ,aAAa,2BACZ,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,MAAK;4BACL,IAAG;4BACH,WAAU;4BACV,UAAU,CAAC,IAAM,sBAAsB,EAAE,MAAM,CAAC,OAAO;;;;;;sCAEzD,8OAAC;4BAAM,SAAQ;4BAAQ,WAAU;;gCAAgB;gCAC5B;8CACnB,8OAAC;oCAAK,WAAU;8CAAgC;;;;;;gCAAY;gCAChD;8CACZ,8OAAC;oCAAK,WAAU;8CAAgC;;;;;;gCAAe;8CAC/D,8OAAC;oCAAK,WAAU;8CAAgC;;;;;;;;;;;;;;;;;;8BAItD,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,2HAAA,CAAA,SAAM;wBACL,WAAU;wBACV,UACE,aACA,KAAK,SAAS,CAAC,YAAY,IAC3B,CAAC,KAAK,SAAS,CAAC,OAAO,IACtB,aAAa,aAAa,CAAC;kCAG7B,aAAa,KAAK,SAAS,CAAC,YAAY,GACrC,aAAa,YACX,kBACA,aAAa,YACb,wBACA,kBACF;;;;;;;;;;;gBAIP,aAAa,0BACZ,8OAAC;oBAAE,WAAU;;wBACV;sCACD,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAM,mHAAA,CAAA,UAAM,CAAC,OAAO;4BACpB,WAAU;sCACX;;;;;;;;;;;yCAKH,8OAAC;oBAAE,WAAU;;wBAAc;wBACA;sCACzB,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAM,mHAAA,CAAA,UAAM,CAAC,OAAO;4BACpB,WAAU;sCACX;;;;;;;;;;;;8BAML,8OAAC;oBACC,WAAW,CAAC,oEAAoE,EAC9E,aAAa,YAAY,UAAU,SACnC;;sCAEF,8OAAC;4BAAK,WAAU;sCAAyB;;;;;;sCACzC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,MAAK;oCACL,WAAU;oCACV,cAAW;8CAEX,cAAA,8OAAC,6HAAA,CAAA,UAAK;wCACJ,KAAI;wCACJ,KAAI;wCACJ,OAAO;wCACP,QAAQ;;;;;;;;;;;8CAGZ,8OAAC;oCACC,MAAK;oCACL,WAAU;oCACV,cAAW;8CAEX,cAAA,8OAAC,6HAAA,CAAA,UAAK;wCACJ,KAAI;wCACJ,KAAI;wCACJ,OAAO;wCACP,QAAQ;;;;;;;;;;;8CAGZ,8OAAC;oCACC,MAAK;oCACL,WAAU;oCACV,cAAW;8CAEX,cAAA,8OAAC,6HAAA,CAAA,UAAK;wCACJ,KAAI;wCACJ,KAAI;wCACJ,OAAO;wCACP,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQxB;uCAEe", "debugId": null}}, {"offset": {"line": 693, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/lib/validations.ts"], "sourcesContent": ["import { z } from \"zod\";\r\n\r\nexport const SignInSchema = z.object({\r\n  email: z\r\n    .string()\r\n    .min(1, { error: \"Email is required\" })\r\n    .email({ error: \"Please provide a valid email address.\" }),\r\n\r\n  password: z\r\n    .string()\r\n    .min(6, { error: \"Password must be at least 6 characters long\" })\r\n    .max(100, { error: \"Password cannot exceed 100 characters\" })\r\n    .regex(/[A-Z]/, \"Password must contain at least one uppercase character\")\r\n    .regex(/[a-z]/, \"Password must contain at least one lowercase character\")\r\n    .regex(/[0-9]/, \"Password must contain at least one number\")\r\n    .regex(\r\n      /[^a-zA-Z0-9]/,\r\n      \"Password must contain at least one special character\"\r\n    ),\r\n});\r\n\r\nexport const SignUpSchema = z.object({\r\n  confirmPassword: z\r\n    .string()\r\n    .min(6, { error: \"Password must be at least 6 characters long\" })\r\n    .max(100, { error: \"Password cannot exceed 100 characters\" })\r\n    .regex(/[A-Z]/, \"Password must contain at least one uppercase character\")\r\n    .regex(/[a-z]/, \"Password must contain at least one lowercase character\")\r\n    .regex(/[0-9]/, \"Password must contain at least one number\")\r\n    .regex(\r\n      /[^a-zA-Z0-9]/,\r\n      \"Password must contain at least one special character\"\r\n    ),\r\n\r\n  name: z\r\n    .string()\r\n    .min(1, { message: \"Name is required.\" })\r\n    .max(50, { message: \"Name cannot exceed 50 characters.\" })\r\n    .regex(/^[a-zA-Z\\s]+$/, {\r\n      message: \"Name can only contain letters and spaces.\",\r\n    }),\r\n\r\n  email: z\r\n    .string()\r\n    .min(1, { error: \"Email is required\" })\r\n    .email({ error: \"Please provide a valid email address.\" }),\r\n\r\n  password: z\r\n    .string()\r\n    .min(6, { error: \"Password must be at least 6 characters long\" })\r\n    .max(100, { error: \"Password cannot exceed 100 characters\" })\r\n    .regex(/[A-Z]/, \"Password must contain at least one uppercase character\")\r\n    .regex(/[a-z]/, \"Password must contain at least one lowercase character\")\r\n    .regex(/[0-9]/, \"Password must contain at least one number\")\r\n    .regex(\r\n      /[^a-zA-Z0-9]/,\r\n      \"Password must contain at least one special character\"\r\n    ),\r\n});\r\n\r\nexport const ForgotPasswordSchema = z.object({\r\n  email: z\r\n    .string()\r\n    .min(1, { error: \"Email is required\" })\r\n    .email({ error: \"Please provide a valid email address.\" }),\r\n});\r\n\r\nexport const SetNewPasswordSchema = z\r\n  .object({\r\n    newPassword: z\r\n      .string()\r\n      .min(6, { message: \"Password must be at least 6 characters long\" })\r\n      .regex(/[A-Z]/, \"Password must contain at least one uppercase character\")\r\n      .regex(/[a-z]/, \"Password must contain at least one lowercase character\")\r\n      .regex(/[0-9]/, \"Password must contain at least one number\")\r\n      .regex(\r\n        /[^a-zA-Z0-9]/,\r\n        \"Password must contain at least one special character\"\r\n      ),\r\n    confirmPassword: z\r\n      .string()\r\n      .min(6, { error: \"Password must be at least 6 characters long\" })\r\n      .max(100, { error: \"Password cannot exceed 100 characters\" }),\r\n  })\r\n  .refine((data) => data.newPassword === data.confirmPassword, {\r\n    message: \"Password do not match\",\r\n    path: [\"confirmPassword\"],\r\n  });\r\n\r\nexport const OtpSchema = z.object({\r\n  otp1: z.string().min(1, \"Required\").regex(/^\\d$/, \"Only digits allowed\"),\r\n  otp2: z.string().min(1, \"Required\").regex(/^\\d$/, \"Only digits allowed\"),\r\n  otp3: z.string().min(1, \"Required\").regex(/^\\d$/, \"Only digits allowed\"),\r\n  otp4: z.string().min(1, \"Required\").regex(/^\\d$/, \"Only digits allowed\"),\r\n});\r\n"], "names": [], "mappings": ";;;;;;;AAAA;;AAEO,MAAM,eAAe,6KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACnC,OAAO,6KAAA,CAAA,IAAC,CACL,MAAM,GACN,GAAG,CAAC,GAAG;QAAE,OAAO;IAAoB,GACpC,KAAK,CAAC;QAAE,OAAO;IAAwC;IAE1D,UAAU,6KAAA,CAAA,IAAC,CACR,MAAM,GACN,GAAG,CAAC,GAAG;QAAE,OAAO;IAA8C,GAC9D,GAAG,CAAC,KAAK;QAAE,OAAO;IAAwC,GAC1D,KAAK,CAAC,SAAS,0DACf,KAAK,CAAC,SAAS,0DACf,KAAK,CAAC,SAAS,6CACf,KAAK,CACJ,gBACA;AAEN;AAEO,MAAM,eAAe,6KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACnC,iBAAiB,6KAAA,CAAA,IAAC,CACf,MAAM,GACN,GAAG,CAAC,GAAG;QAAE,OAAO;IAA8C,GAC9D,GAAG,CAAC,KAAK;QAAE,OAAO;IAAwC,GAC1D,KAAK,CAAC,SAAS,0DACf,KAAK,CAAC,SAAS,0DACf,KAAK,CAAC,SAAS,6CACf,KAAK,CACJ,gBACA;IAGJ,MAAM,6KAAA,CAAA,IAAC,CACJ,MAAM,GACN,GAAG,CAAC,GAAG;QAAE,SAAS;IAAoB,GACtC,GAAG,CAAC,IAAI;QAAE,SAAS;IAAoC,GACvD,KAAK,CAAC,iBAAiB;QACtB,SAAS;IACX;IAEF,OAAO,6KAAA,CAAA,IAAC,CACL,MAAM,GACN,GAAG,CAAC,GAAG;QAAE,OAAO;IAAoB,GACpC,KAAK,CAAC;QAAE,OAAO;IAAwC;IAE1D,UAAU,6KAAA,CAAA,IAAC,CACR,MAAM,GACN,GAAG,CAAC,GAAG;QAAE,OAAO;IAA8C,GAC9D,GAAG,CAAC,KAAK;QAAE,OAAO;IAAwC,GAC1D,KAAK,CAAC,SAAS,0DACf,KAAK,CAAC,SAAS,0DACf,KAAK,CAAC,SAAS,6CACf,KAAK,CACJ,gBACA;AAEN;AAEO,MAAM,uBAAuB,6KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC3C,OAAO,6KAAA,CAAA,IAAC,CACL,MAAM,GACN,GAAG,CAAC,GAAG;QAAE,OAAO;IAAoB,GACpC,KAAK,CAAC;QAAE,OAAO;IAAwC;AAC5D;AAEO,MAAM,uBAAuB,6KAAA,CAAA,IAAC,CAClC,MAAM,CAAC;IACN,aAAa,6KAAA,CAAA,IAAC,CACX,MAAM,GACN,GAAG,CAAC,GAAG;QAAE,SAAS;IAA8C,GAChE,KAAK,CAAC,SAAS,0DACf,KAAK,CAAC,SAAS,0DACf,KAAK,CAAC,SAAS,6CACf,KAAK,CACJ,gBACA;IAEJ,iBAAiB,6KAAA,CAAA,IAAC,CACf,MAAM,GACN,GAAG,CAAC,GAAG;QAAE,OAAO;IAA8C,GAC9D,GAAG,CAAC,KAAK;QAAE,OAAO;IAAwC;AAC/D,GACC,MAAM,CAAC,CAAC,OAAS,KAAK,WAAW,KAAK,KAAK,eAAe,EAAE;IAC3D,SAAS;IACT,MAAM;QAAC;KAAkB;AAC3B;AAEK,MAAM,YAAY,6KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAChC,MAAM,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,YAAY,KAAK,CAAC,QAAQ;IAClD,MAAM,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,YAAY,KAAK,CAAC,QAAQ;IAClD,MAAM,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,YAAY,KAAK,CAAC,QAAQ;IAClD,MAAM,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,YAAY,KAAK,CAAC,QAAQ;AACpD", "debugId": null}}, {"offset": {"line": 772, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/app/%28auth%29/sign-in/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\nimport Image from \"next/image\";\r\nimport AuthForm from \"@/components/forms/AuthForm\";\r\nimport { SignInSchema } from \"@/lib/validations\"; // Make sure this schema exists\r\n\r\nconst SignIn = () => {\r\n  const placeholderValues = {\r\n    email: \"Enter your email address\",\r\n    password: \"Enter your password\",\r\n  };\r\n\r\n  return (\r\n    <div className=\"min-h-screen lg:h-[150vh] lg:relative\">\r\n      {/* Background image container with 140% height */}\r\n      <div className=\"hidden lg:block lg:absolute lg:top-0 lg:left-0 lg:w-[60%] lg:h-[150vh] lg:min-h-[150vh]\">\r\n        <Image\r\n          src=\"/images/bgauthimg.png\"\r\n          alt=\"auth\"\r\n          fill\r\n          className=\"object-cover\"\r\n          priority\r\n        />\r\n      </div>\r\n\r\n      {/* Mobile layout */}\r\n      <div className=\"lg:hidden\">\r\n        {/* Top image for mobile/tablet */}\r\n        <div className=\"relative w-full h-60 overflow-hidden rounded-b-4xl\">\r\n          <Image\r\n            src=\"/images/bgauthimg.png\"\r\n            alt=\"auth\"\r\n            fill\r\n            className=\"object-cover\"\r\n            priority\r\n          />\r\n        </div>\r\n\r\n        {/* Form Section for mobile */}\r\n        <div className=\"bg-white px-4 sm:px-6 py-8\">\r\n          <div className=\"mx-auto max-w-sm sm:max-w-lg md:max-w-3xl\">\r\n            <AuthForm\r\n              formType=\"SIGN_IN\"\r\n              schema={SignInSchema}\r\n              defaultValues={{\r\n                email: \"\",\r\n                password: \"\",\r\n              }}\r\n              onSubmit={(data) => Promise.resolve({ success: true, data })}\r\n              heading=\"Sign In - For Applicants\"\r\n              placeholderValues={placeholderValues}\r\n            />\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Desktop Form Section with proper rounded corners */}\r\n      <div className=\"hidden lg:block lg:absolute lg:right-0 lg:top-0 lg:w-[48%] lg:h-[150vh] lg:min-h-[150vh]\">\r\n        <div className=\"bg-white lg:rounded-l-4xl h-full\">\r\n          <div className=\"flex justify-center px-4 sm:px-6 py-13 h-full\">\r\n            <div className=\"w-full max-w-md\">\r\n              <AuthForm\r\n                formType=\"SIGN_IN\"\r\n                schema={SignInSchema}\r\n                defaultValues={{\r\n                  email: \"\",\r\n                  password: \"\",\r\n                }}\r\n                onSubmit={(data) => Promise.resolve({ success: true, data })}\r\n                heading=\"Sign In - For Applicants\"\r\n                placeholderValues={placeholderValues}\r\n              />\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default SignIn;\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA,yMAAkD,+BAA+B;AALjF;;;;;AAOA,MAAM,SAAS;IACb,MAAM,oBAAoB;QACxB,OAAO;QACP,UAAU;IACZ;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;oBACJ,KAAI;oBACJ,KAAI;oBACJ,IAAI;oBACJ,WAAU;oBACV,QAAQ;;;;;;;;;;;0BAKZ,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;4BACJ,KAAI;4BACJ,KAAI;4BACJ,IAAI;4BACJ,WAAU;4BACV,QAAQ;;;;;;;;;;;kCAKZ,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,gIAAA,CAAA,UAAQ;gCACP,UAAS;gCACT,QAAQ,kHAAA,CAAA,eAAY;gCACpB,eAAe;oCACb,OAAO;oCACP,UAAU;gCACZ;gCACA,UAAU,CAAC,OAAS,QAAQ,OAAO,CAAC;wCAAE,SAAS;wCAAM;oCAAK;gCAC1D,SAAQ;gCACR,mBAAmB;;;;;;;;;;;;;;;;;;;;;;0BAO3B,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,gIAAA,CAAA,UAAQ;gCACP,UAAS;gCACT,QAAQ,kHAAA,CAAA,eAAY;gCACpB,eAAe;oCACb,OAAO;oCACP,UAAU;gCACZ;gCACA,UAAU,CAAC,OAAS,QAAQ,OAAO,CAAC;wCAAE,SAAS;wCAAM;oCAAK;gCAC1D,SAAQ;gCACR,mBAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQnC;uCAEe", "debugId": null}}]}