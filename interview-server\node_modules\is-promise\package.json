{"name": "is-promise", "version": "4.0.0", "description": "Test whether an object looks like a promises-a+ promise", "main": "./index.js", "scripts": {"test": "node test"}, "files": ["index.js", "index.mjs", "index.d.ts"], "exports": {".": [{"import": "./index.mjs", "require": "./index.js", "default": "./index.js"}, "./index.js"]}, "repository": {"type": "git", "url": "https://github.com/then/is-promise.git"}, "author": "ForbesLindesay", "license": "MIT"}