"use client";

import React from "react";
import PasswordForm from "@/components/forms/forgetpasswordprocess";
import { OtpSchema } from "@/lib/validations";
import Image from "next/image";
import { z } from "zod";

type OtpInput = z.infer<typeof OtpSchema>;

const OtpPage = () => {
  const defaultValues = {
    otp1: "",
    otp2: "",
    otp3: "",
    otp4: "",
  };

  const onSubmit = async (data: OtpInput) => {
    console.log("OTP Submitted:", data);
    return { success: true };
  };

  return (
    <div className="min-h-screen lg:h-[150vh] lg:relative">
      {/* Background image container with 140% height */}
      <div className="hidden lg:block lg:absolute lg:top-0 lg:left-0 lg:w-[60%] lg:h-[150vh] lg:min-h-[150vh]">
        <Image
          src="/images/bgauthimg.png"
          alt="auth"
          fill
          className="object-cover"
          priority
        />
      </div>

      {/* Mobile layout */}
      <div className="lg:hidden">
        {/* Top image for mobile/tablet */}
        <div className="relative w-full h-60 overflow-hidden rounded-b-4xl">
          <Image
            src="/images/bgauthimg.png"
            alt="auth"
            fill
            className="object-cover"
            priority
          />
        </div>

        {/* Form Section for mobile */}
        <div className="bg-white px-4 sm:px-6 py-8">
          <div className="mx-auto max-w-sm sm:max-w-lg md:max-w-3xl">
            <PasswordForm
              mode="otp"
              schema={OtpSchema}
              defaultValues={defaultValues}
              onSubmit={onSubmit}
            />
          </div>
        </div>
      </div>

      {/* Desktop Form Section with proper rounded corners */}
      <div className="hidden lg:block lg:absolute lg:right-0 lg:top-0 lg:w-[48%] lg:h-[150vh] lg:min-h-[150vh]">
        <div className="bg-white lg:rounded-l-4xl h-full">
          <div className="flex justify-center px-4 sm:px-6 py-13 h-full">
            <div className="w-full max-w-md">
              <PasswordForm
                mode="otp"
                schema={OtpSchema}
                defaultValues={defaultValues}
                onSubmit={onSubmit}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OtpPage;
