"use client";
import { useInterview } from "@/context/InterviewContext";

type QuestionsListProps = {
  className?: string;
};

const QuestionsList = ({ className }: QuestionsListProps) => {
  const { conversationHistory, isInterviewStarted } = useInterview();

  // Filter to show only interviewer questions
  const interviewerQuestions = conversationHistory.filter(msg => msg.role === 'interviewer');

  return (
    <div
      className={`rounded-2xl bg-white p-4 w-full shadow-sm overflow-y-auto scrollbar-hidden ${
        className || ""
      }`}
    >
      <h3 className="font-semibold text-lg mb-6">Video Transcript</h3>

      {!isInterviewStarted ? (
        <p className="text-gray-500 text-center py-8">Interview not started yet</p>
      ) : interviewerQuestions.length === 0 ? (
        <p className="text-gray-500 text-center py-8">Loading questions...</p>
      ) : (
        <ul className="space-y-4">
          {interviewerQuestions.map((question, i) => (
            <li
              key={i}
              className="relative flex items-start space-x-3 p-3 bg-gray-50 rounded-lg"
            >
              <div className="rounded-full w-8 h-8 flex items-center justify-center text-sm font-medium bg-[#6938EF] text-white flex-shrink-0">
                {i + 1}
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm text-gray-800 leading-relaxed">
                  {question.content}
                </p>
              </div>
            </li>
          ))}
        </ul>
      )}
    </div>
  );
};

export default QuestionsList;
