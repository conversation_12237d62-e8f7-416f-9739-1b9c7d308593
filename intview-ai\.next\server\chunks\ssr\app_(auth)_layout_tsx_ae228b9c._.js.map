{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/app/%28auth%29/layout.tsx"], "sourcesContent": ["// import SocialAuthForm from \"@/components/forms/SocialAuthForm\";\r\n// import Image from \"next/image\";\r\nimport { ReactNode } from \"react\";\r\n\r\nconst AuthLayout = ({ children }: { children: ReactNode }) => {\r\n  return (\r\n    <main >\r\n      \r\n        \r\n        {children}\r\n        {/* <SocialAuthForm /> */}\r\n    </main>\r\n  );\r\n};\r\n\r\nexport default AuthLayout;\r\n"], "names": [], "mappings": "AAAA,kEAAkE;AAClE,kCAAkC;;;;;;AAGlC,MAAM,aAAa,CAAC,EAAE,QAAQ,EAA2B;IACvD,qBACE,8OAAC;kBAGI;;;;;;AAIT;uCAEe", "debugId": null}}]}