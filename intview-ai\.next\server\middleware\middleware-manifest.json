{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_5399b416._.js", "server/edge/chunks/node_modules_@auth_core_5ebafa38._.js", "server/edge/chunks/node_modules_jose_dist_webapi_49ff121e._.js", "server/edge/chunks/node_modules_e184ff1b._.js", "server/edge/chunks/[root-of-the-server]__df53d061._.js", "server/edge/chunks/edge-wrapper_3d09a47d.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "9R+Wv7DZZYhX4pQr08UnGwtOukSaA3NFvRrMYN48HJE=", "__NEXT_PREVIEW_MODE_ID": "92abf8bc1796f7b6e1ed848df177d120", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "81afe4a427515e49cdfdea9fb1587fdef30e160245402cbec3eaedf56097a3bd", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "14767b8fb188f31b96ca957d070cddd7ef65fca3c4a6c568ee84531941048205"}}}, "instrumentation": null, "functions": {}}