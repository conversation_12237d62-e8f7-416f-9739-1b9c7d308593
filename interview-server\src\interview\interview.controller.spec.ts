import { Test, TestingModule } from '@nestjs/testing';
import { Interview<PERSON><PERSON>roller } from './interview.controller';
import { InterviewService } from './interview.service';
import { HttpException, HttpStatus } from '@nestjs/common';

describe('InterviewController', () => {
  let controller: InterviewController;
  let service: InterviewService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [InterviewController],
      providers: [InterviewService],
    }).compile();

    controller = module.get<InterviewController>(InterviewController);
    service = module.get<InterviewService>(InterviewService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('runInterview', () => {
    it('should return interview response for valid input', async () => {
      const mockRequest = {
        position: 'Software Developer',
        name: '<PERSON>',
        experience: 3,
        history: [
          {
            role: 'candidate' as const,
            content: 'Hi, I\'m <PERSON>'
          },
          {
            role: 'interviewer' as const,
            content: 'Nice to meet you, <PERSON>. Tell me about your experience.'
          }
        ]
      };

      const mockResponse = {
        response: 'Mock interview response'
      };

      jest.spyOn(service, 'runInterview').mockResolvedValue(mockResponse);

      const result = await controller.runInterview(mockRequest);
      expect(result).toEqual(mockResponse);
      expect(service.runInterview).toHaveBeenCalledWith(mockRequest);
    });

    it('should throw BadRequest exception for missing required fields', async () => {
      const invalidRequest = {
        position: '',
        name: 'John Doe',
        experience: 3,
        history: []
      };

      await expect(controller.runInterview(invalidRequest))
        .rejects
        .toThrow(new HttpException(
          'Missing required fields: position, name, and experience are required',
          HttpStatus.BAD_REQUEST
        ));
    });

    it('should default history to empty array if not provided', async () => {
      const requestWithoutHistory = {
        position: 'Software Developer',
        name: 'John Doe',
        experience: 3,
        history: undefined as any
      };

      const mockResponse = {
        response: 'Mock interview response'
      };

      jest.spyOn(service, 'runInterview').mockResolvedValue(mockResponse);

      await controller.runInterview(requestWithoutHistory);

      expect(service.runInterview).toHaveBeenCalledWith({
        ...requestWithoutHistory,
        history: []
      });
    });
  });
});
