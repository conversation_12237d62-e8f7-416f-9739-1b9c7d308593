export interface ConversationMessage {
  role: 'candidate' | 'interviewer';
  content: string;
}

export interface InterviewRequest {
  position: string;
  name: string;
  experience: number;
  history: ConversationMessage[];
}

export interface ScoreCard {
  technicalSkills: number;
  problemSolving: number;
  communication: number;
  experience: number;
  overall: number;
}

export interface Summary {
  ScoreCard: ScoreCard;
  recommendation: string;
  reason: string;
}

export interface InterviewResponse {
  nextQuestion: string;
  currentQuestionScore: number;
  isInterviewCompleted: boolean;
  Summary?: Summary;
}

class InterviewApiService {
  private baseUrl: string;

  constructor() {
    this.baseUrl = 'https://interview-server-delta.vercel.app';
  }

  async sendInterviewRequest(request: InterviewRequest): Promise<InterviewResponse> {
    try {
      const response = await fetch(`${this.baseUrl}/interview`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Interview API Error: ${response.status} ${response.statusText} - ${errorText}`);
      }

      const data: InterviewResponse = await response.json();
      return data;
    } catch (error) {
      console.error('Failed to send interview request:', error);
      throw error;
    }
  }

  async startInterview(position: string, name: string, experience: number): Promise<InterviewResponse> {
    const request: InterviewRequest = {
      position,
      name,
      experience,
      history: []
    };

    const response = await this.sendInterviewRequest(request);
    return response;
  }

  async continueInterview(
    position: string,
    name: string,
    experience: number,
    history: ConversationMessage[]
  ): Promise<InterviewResponse> {
    const request: InterviewRequest = {
      position,
      name,
      experience,
      history
    };

    const response = await this.sendInterviewRequest(request);
    return response;
  }
}

// Export singleton instance
export const interviewApi = new InterviewApiService();
