"use client";

import { useRef, useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useRouter } from "next/navigation";
import Image from "next/image";

// Inside your component, add this state:
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";

type Mode = "forgot" | "reset" | "otp";

interface PasswordFormProps {
  mode: Mode;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  schema: any;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  defaultValues: any;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  onSubmit: (data: any) => Promise<{ success: boolean }>;
}

const PasswordForm = ({
  mode,
  schema,
  defaultValues,
  onSubmit,
}: PasswordFormProps) => {
  const router = useRouter();

  const form = useForm({
    resolver: zodResolver(schema),
    defaultValues,
    mode: "onChange", // enables real-time validation
  });

  const otpRef1 = useRef<HTMLInputElement>(null);
  const otpRef2 = useRef<HTMLInputElement>(null);
  const otpRef3 = useRef<HTMLInputElement>(null);
  const otpRef4 = useRef<HTMLInputElement>(null);
  const otpRefs = [otpRef1, otpRef2, otpRef3, otpRef4];
  const [showPassword, setShowPassword] = useState(false);

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const handleSubmit = async (data: any) => {
    const result = await onSubmit(data);

    if (result.success) {
      if (mode === "forgot") {
        router.push("/sign-in/Forgotpassword/verifyotp");
      } else if (mode === "otp") {
        router.push("/sign-in/Forgotpassword/verifyotp/Setnewpassword");
      } else if (mode === "reset") {
        router.push("/sign-in");
      }
    }
  };

  const handleOtpChange = (
    index: number,
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const value = e.target.value;
    form.setValue(`otp${index + 1}`, value);

    if (value && index < otpRefs.length - 1) {
      otpRefs[index + 1].current?.focus();
    }
  };

  return (
    <div className="w-full px-4 py-6 lg:max-w-md lg:min-w-[28rem]">
      <h1 className="text-[28px] lg:text-[34px] leading-[36px] lg:leading-[41px] font-semibold font-poppins text-[#100F14] mb-2">
        {mode === "forgot"
          ? "Forget Password"
          : mode === "reset"
            ? "Reset Password"
            : "Verification Code"}
      </h1>

      <p className="text-gray-600 font-medium mb-8 text-poppins">
        {mode === "forgot"
          ? "Enter your email id to request a password reset."
          : mode === "reset"
            ? "Please create a new password"
            : "Enter OTP sent to your email for the verification process."}
      </p>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
          {mode === "otp" ? (
            <>
              <h4 className="text-lg font-medium mb-3">OTP</h4>
              <div className="flex gap-4 justify-between">
                {otpRefs.map((_, index) => (
                  <FormField
                    key={index}
                    control={form.control}
                    name={`otp${index + 1}`}
                    render={({ field }) => {
                      const { ref, onChange, ...rest } = field;

                      return (
                        <FormItem className="flex-1">
                          <FormControl>
                            <Input
                              ref={(el) => {
                                ref(el);
                                otpRefs[index].current = el;
                              }}
                              maxLength={1}
                              className="text-center font-bold text-3xl py-8 sm:py-7"
                              type="text"
                              inputMode="numeric"
                              pattern="[0-9]*"
                              onChange={(e) => {
                                onChange(e);
                                handleOtpChange(index, e);
                              }}
                              {...rest}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      );
                    }}
                  />
                ))}
              </div>
            </>
          ) : (
            Object.keys(defaultValues).map((fieldName) => (
              <FormField
                key={fieldName}
                control={form.control}
                name={fieldName}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-[#2E2E2E]">
                      {fieldName === "email"
                        ? "Email Address"
                        : fieldName === "password" ||
                            fieldName === "newPassword"
                          ? "Password"
                          : "Confirm Password"}
                    </FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Input
                          type={
                            fieldName.toLowerCase().includes("password") &&
                            !showPassword
                              ? "password"
                              : "text"
                          }
                          placeholder={
                            fieldName === "email"
                              ? "Enter Your Email address"
                              : fieldName === "password" ||
                                  fieldName === "newPassword"
                                ? "Enter Your Password"
                                : "Confirm Your Password"
                          }
                          className="py-5 sm:py-6 pr-12" // space for eye icon
                          {...field}
                        />
                        {fieldName.toLowerCase().includes("password") && (
                          <button
                            type="button"
                            onClick={() => setShowPassword((prev) => !prev)}
                            className="absolute right-4 top-1/2 transform -translate-y-1/2 hover:cursor-pointer"
                          >
                            <Image
                              src="/images/eye.png"
                              alt="Toggle Password Visibility"
                              width={20}
                              height={20}
                            />
                          </button>
                        )}
                      </div>
                    </FormControl>

                    <FormMessage />
                  </FormItem>
                )}
              />
            ))
          )}
          <div className="flex justify-center">
            <Button
              type="submit"
              className="lg:w-[360px] w-full  bg-[#6938EF] hover:bg-[#682ed6] hover:cursor-pointer text-white py-5 sm:py-6 px-4 rounded-full mt-5"
              disabled={form.formState.isSubmitting || !form.formState.isValid}
            >
              {form.formState.isSubmitting
                ? mode === "forgot"
                  ? "Sending..."
                  : mode === "reset"
                    ? "Resetting..."
                    : "Verifying..."
                : mode === "forgot"
                  ? "Continue"
                  : mode === "reset"
                    ? "Reset Password"
                    : "Continue"}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
};

export default PasswordForm;
